import { FontAwesome, Ionicons, MaterialIcons } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import * as ImagePicker from 'expo-image-picker';
import React, { useEffect, useRef, useState } from 'react';
import {
    Alert,
    FlatList,
    Image,
    KeyboardAvoidingView,
    PermissionsAndroid,
    Platform,
    SafeAreaView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import { Socket } from 'socket.io-client';

// Define types
interface MessageData {
  room: string;
  author: string;
  message: string;
  time: string;
  type: 'text' | 'image' | 'audio' | 'system';
}

interface ChatsProps {
  socket: Socket;
  userName: string;
  room: string;
}

interface UserData {
  userName: string;
  userCount: number;
}

const Chats: React.FC<ChatsProps> = ({ socket, userName, room }) => {
  const [currentMessage, setCurrentMessage] = useState<string>('');
  const [messageList, setMessageList] = useState<MessageData[]>([]);
  const [userCount, setUserCount] = useState<number>(1);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [recordingTime, setRecordingTime] = useState<number>(0);
  const [hasAudioPermission, setHasAudioPermission] = useState<boolean>(false);
  const [playingAudio, setPlayingAudio] = useState<{ [key: string]: Audio.Sound }>({});
  const [currentlyPlaying, setCurrentlyPlaying] = useState<string | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const flatListRef = useRef<FlatList<MessageData> | null>(null);

  // Request microphone permission
  useEffect(() => {
    const requestPermissions = async () => {
      try {
        if (Platform.OS === 'android') {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
            {
              title: 'Microphone Permission',
              message: 'This app needs access to your microphone to record audio.',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            }
          );
          if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
            console.log('Audio permission denied');
            setHasAudioPermission(false);
            return;
          }
        }

        const { status } = await Audio.requestPermissionsAsync();
        if (status !== 'granted') {
          console.log('Audio permission denied');
          setHasAudioPermission(false);
          Alert.alert(
            'Permission Required',
            'Please enable microphone permission in your device settings to record audio messages.'
          );
          return;
        }

        setHasAudioPermission(true);
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: true,
          playsInSilentModeIOS: true,
        });
      } catch (error) {
        console.error('Error requesting permissions:', error);
        setHasAudioPermission(false);
      }
    };

    requestPermissions();
  }, []);

  const startRecording = async (): Promise<void> => {
    if (!hasAudioPermission) {
      Alert.alert(
        'Permission Required',
        'Microphone permission is required to record audio. Please enable it in your device settings.',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Settings', 
            onPress: () => {
              // On iOS, this would open app settings
              // On Android, you might need to use a library like react-native-permissions
              console.log('Open settings');
            }
          }
        ]
      );
      return;
    }

    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });
      
      const { recording: newRecording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      
      setRecording(newRecording);
      setIsRecording(true);
      setRecordingTime(0);
      
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } catch (err) {
      console.error('Failed to start recording', err);
      Alert.alert('Recording Error', 'Failed to start recording. Please try again.');
    }
  };

  const stopRecording = async (): Promise<void> => {
    try {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      
      setIsRecording(false);
      
      if (!recording) {
        console.error('No recording found');
        return;
      }
      
      await recording.stopAndUnloadAsync();
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
      });
      
      const uri = recording.getURI();
      if (uri) {
        const audioData: MessageData = {
          room: room,
          author: userName,
          message: uri,
          time: new Date(Date.now()).getHours() + ":" + new Date(Date.now()).getMinutes().toString().padStart(2, '0'),
          type: 'audio'
        };
        
        socket.emit("send_audio", audioData);
        setMessageList((list) => [audioData, ...list]);
        
        // Scroll to top after adding message (since we're using inverted list)
        setTimeout(() => {
          flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
        }, 100);
      }
      
      setRecording(null);
    } catch (err) {
      console.error('Failed to stop recording', err);
      Alert.alert('Recording Error', 'Failed to save recording. Please try again.');
    }
  };

  const sendMessage = async (): Promise<void> => {
    if (currentMessage.trim() !== '') {
      const msgData: MessageData = {
        room: room,
        author: userName,
        message: currentMessage.trim(),
        time: new Date(Date.now()).getHours() + ":" + new Date(Date.now()).getMinutes().toString().padStart(2, '0'),
        type: 'text'
      };
      
      socket.emit("send_message", msgData);
      setMessageList((list) => [msgData, ...list]);
      setCurrentMessage("");
      
      // Scroll to top after adding message (since we're using inverted list)
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
      }, 100);
    }
  };

  const pickImage = async (): Promise<void> => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const imgData: MessageData = {
          room: room,
          author: userName,
          message: result.assets[0].uri,
          time: new Date(Date.now()).getHours() + ":" + new Date(Date.now()).getMinutes().toString().padStart(2, '0'),
          type: 'image'
        };
        
        socket.emit("send_image", imgData);
        setMessageList((list) => [imgData, ...list]);
        
        // Scroll to top after adding message (since we're using inverted list)
        setTimeout(() => {
          flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
        }, 100);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Image Error', 'Failed to select image. Please try again.');
    }
  };

  useEffect(() => {
    const handleReceiveMessage = (data: MessageData) => {
      const messageData = { ...data, type: 'text' as const };
      setMessageList((list) => [messageData, ...list]);
      // Scroll to top when receiving new message (since we're using inverted list)
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
      }, 100);
    };

    const handleReceiveImage = (data: MessageData) => {
      const imageData = { ...data, type: 'image' as const };
      setMessageList((list) => [imageData, ...list]);
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
      }, 100);
    };

    const handleReceiveAudio = (data: MessageData) => {
      const audioData = { ...data, type: 'audio' as const };
      setMessageList((list) => [audioData, ...list]);
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
      }, 100);
    };

    const handleUserJoined = (data: UserData) => {
      setUserCount(data.userCount);
      if (data.userName !== userName) {
        const joinMessage: MessageData = {
          room: room,
          author: "System",
          message: `${data.userName} has joined the room`,
          time: new Date(Date.now()).getHours() + ":" + new Date(Date.now()).getMinutes().toString().padStart(2, '0'),
          type: 'system'
        };
        setMessageList((list) => [joinMessage, ...list]);
        setTimeout(() => {
          flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
        }, 100);
      }
    };
    
    const handleUserLeft = (data: UserData) => {
      setUserCount(data.userCount);
      const leaveMessage: MessageData = {
        room: room,
        author: "System",
        message: `${data.userName} has left the room`,
        time: new Date(Date.now()).getHours() + ":" + new Date(Date.now()).getMinutes().toString().padStart(2, '0'),
        type: 'system'
      };
      setMessageList((list) => [leaveMessage, ...list]);
      setTimeout(() => {
        flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
      }, 100);
    };
    
    const handleRoomUsers = (data: UserData) => {
      setUserCount(data.userCount);
    };

    socket.on("receive_message", handleReceiveMessage);
    socket.on("receive_image", handleReceiveImage);
    socket.on("receive_audio", handleReceiveAudio);
    socket.on("user_joined", handleUserJoined);
    socket.on("user_left", handleUserLeft);
    socket.on("room_users", handleRoomUsers);

    return () => {
      socket.off("receive_message", handleReceiveMessage);
      socket.off("receive_image", handleReceiveImage);
      socket.off("receive_audio", handleReceiveAudio);
      socket.off("user_joined", handleUserJoined);
      socket.off("user_left", handleUserLeft);
      socket.off("room_users", handleRoomUsers);
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      // Cleanup audio players
      Object.values(playingAudio).forEach(async (sound) => {
        try {
          await sound.unloadAsync();
        } catch (error) {
          console.log('Error unloading sound:', error);
        }
      });
    };
  }, [socket, room, userName]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = (seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
  };

  const playAudio = async (uri: string, messageKey: string) => {
    try {
      // Stop currently playing audio if any
      if (currentlyPlaying && playingAudio[currentlyPlaying]) {
        await playingAudio[currentlyPlaying].stopAsync();
        setCurrentlyPlaying(null);
      }

      // If clicking the same audio that was playing, just stop it
      if (currentlyPlaying === messageKey) {
        return;
      }

      const { sound } = await Audio.Sound.createAsync(
        { uri },
        { shouldPlay: true }
      );

      setPlayingAudio(prev => ({ ...prev, [messageKey]: sound }));
      setCurrentlyPlaying(messageKey);

      sound.setOnPlaybackStatusUpdate((status) => {
        if (status.isLoaded && status.didJustFinish) {
          setCurrentlyPlaying(null);
          sound.unloadAsync();
          setPlayingAudio(prev => {
            const { [messageKey]: removed, ...rest } = prev;
            return rest;
          });
        }
      });
    } catch (error) {
      console.error('Error playing audio:', error);
      Alert.alert('Audio Error', 'Failed to play audio message.');
    }
  };

  const renderItem = ({ item, index }: { item: MessageData; index: number }) => {
    const messageKey = `${item.author}-${item.time}-${index}`;
    const isPlaying = currentlyPlaying === messageKey;
    
    return (
      <View style={[
        styles.messageContainer,
        item.author === userName ? styles.youContainer : styles.otherContainer,
        item.type === 'system' && styles.systemContainer
      ]}>
        {item.type === 'text' ? (
          <Text style={[
            styles.messageText,
            item.author === userName ? styles.youText : styles.otherText,
            item.type === 'system' && styles.systemText
          ]}>{item.message}</Text>
        ) : item.type === 'image' ? (
          <Image 
            source={{ uri: item.message }} 
            style={styles.image} 
            resizeMode="contain"
          />
        ) : item.type === 'audio' ? (
          <TouchableOpacity 
            style={styles.audioContainer}
            onPress={() => playAudio(item.message, messageKey)}
          >
            <FontAwesome 
              name={isPlaying ? "pause" : "play"} 
              size={24} 
              color="white" 
            />
            <Text style={styles.audioText}>
              {isPlaying ? "Playing..." : "Audio message"}
            </Text>
          </TouchableOpacity>
        ) : (
          <Text style={styles.systemText}>{item.message}</Text>
        )}
        <View style={styles.messageMeta}>
          <Text style={styles.timeText}>{item.time}</Text>
          {item.type !== 'system' && (
            <Text style={styles.authorText}>{item.author}</Text>
          )}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerText}>Room: {room} ({userCount} users)</Text>
        {isRecording && (
          <View style={styles.recordingIndicator}>
            <View style={styles.recordingDot} />
            <Text style={styles.recordingText}>Recording... {formatTime(recordingTime)}</Text>
          </View>
        )}
      </View>
      
      <KeyboardAvoidingView 
        style={styles.chatContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={0}
      >
        <FlatList
          ref={flatListRef}
          data={messageList}
          renderItem={renderItem}
          keyExtractor={(item, index) => `${item.author}-${item.time}-${index}`}
          contentContainerStyle={styles.messagesList}
          inverted
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        />
        
        <View style={styles.footer}>
          {isRecording ? (
            <View style={styles.recordingControls}>
              <Text style={styles.recordingTime}>{formatTime(recordingTime)}</Text>
              <TouchableOpacity 
                style={styles.stopButton}
                onPress={stopRecording}
              >
                <Text style={styles.stopButtonText}>Stop</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <>
              <TextInput
                style={styles.input}
                value={currentMessage}
                placeholder="Type..."
                placeholderTextColor="#666"
                onChangeText={setCurrentMessage}
                onSubmitEditing={sendMessage}
                multiline
                maxLength={1000}
                blurOnSubmit={false}
                returnKeyType="send"
              />
              <TouchableOpacity 
                style={styles.iconButton}
                onPress={pickImage}
              >
                <MaterialIcons name="image" size={24} color="white" />
              </TouchableOpacity>
            </>
          )}
          <TouchableOpacity 
            style={[
              styles.iconButton,
              isRecording && { backgroundColor: 'red' },
              !hasAudioPermission && { opacity: 0.5 }
            ]}
            onPress={isRecording ? stopRecording : startRecording}
            disabled={!hasAudioPermission && !isRecording}
          >
            <Ionicons 
              name={isRecording ? "mic" : "mic-outline"} 
              size={24} 
              color="white" 
            />
          </TouchableOpacity>
          {!isRecording && (
            <TouchableOpacity 
              style={styles.iconButton}
              onPress={sendMessage}
            >
              <Ionicons name="send" size={24} color="white" />
            </TouchableOpacity>
          )}
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#070707',
  },
  chatContainer: {
    flex: 1,
    width: '100%',
    
    backgroundColor: '#070707',
  },
  header: {
    height: 60,
    backgroundColor: '#1a1919',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 10,
  },
  headerText: {
    color: 'white',
    fontSize: 16,
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 10,
  },
  recordingDot: {
    width: 10,
    height: 10,
    backgroundColor: 'red',
    borderRadius: 5,
    marginRight: 5,
  },
  recordingText: {
    color: 'white',
    fontSize: 14,
  },
  messagesList: {
    padding: 10,
    paddingBottom: 20,
  },
  messageContainer: {
    maxWidth: '80%',
    marginBottom: 10,
    padding: 10,
    borderRadius: 10,
  },
  youContainer: {
    alignSelf: 'flex-end',
    backgroundColor: '#0084ff',
    borderTopRightRadius: 0,
  },
  otherContainer: {
    alignSelf: 'flex-start',
    backgroundColor: '#727475',
    borderTopLeftRadius: 0,
  },
  systemContainer: {
    alignSelf: 'center',
    backgroundColor: 'transparent',
    marginVertical: 5,
  },
  messageText: {
    color: 'white',
    fontSize: 16,
  },
  youText: {
    color: 'white',
  },
  otherText: {
    color: 'white',
  },
  systemText: {
    color: '#aaa',
    fontStyle: 'italic',
  },
  image: {
    width: 200,
    height: 200,
    borderRadius: 5,
  },
  audioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
  },
  audioText: {
    color: 'white',
    marginLeft: 10,
    fontSize: 14,
  },
  messageMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 5,
  },
  timeText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
  },
  authorText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  footer: {
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    backgroundColor: '#0c0c0c',
  },
  input: {
    flex: 1,
    minHeight: 40,
    maxHeight: 100,
    backgroundColor: 'black',
    color: 'white',
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginRight: 10,
    textAlignVertical: 'center',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#1a1a1a',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 5,
  },
  recordingControls: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
    borderRadius: 20,
    paddingHorizontal: 15,
    height: 40,
  },
  recordingTime: {
    color: 'red',
    fontWeight: 'bold',
    fontSize: 16,
  },
  stopButton: {
    backgroundColor: 'red',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 15,
  },
  stopButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default Chats;