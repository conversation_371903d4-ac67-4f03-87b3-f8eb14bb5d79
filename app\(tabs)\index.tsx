import Chats from '@/components/Chats';
import React, { useState } from 'react';
import { StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';
import io from 'socket.io-client';

const socket = io('https://temp-chat-server.onrender.com');

export default function App() {
  const [userName, setUserName] = useState("");
  const [room, setRoom] = useState("");
  const [show, setShow] = useState(false);

  const joinRoom = () => {
    if (userName !== "" && room !== "") {
      socket.emit("join_room", { room, userName });
      setShow(true);
    }
  };

  return (
    <View style={styles.container}>
      {!show ? (
        <View style={styles.joinContainer}>
          <View style={styles.headerContainer}>
            <Text style={styles.headerText}>Join the room</Text>
            <Text style={styles.subHeaderText}>Temporary chat app for anonymous use</Text>
          </View>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="Type your name"
              onChangeText={setUserName}
              value={userName}
            />
            <TextInput
              style={styles.input}
              placeholder="Type any code with your friends"
              onChangeText={setRoom}
              value={room}
            />
            <TouchableOpacity style={styles.button} onPress={joinRoom}>
              <Text style={styles.buttonText}>Start chat</Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <Chats socket={socket} userName={userName} room={room} />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#242526',
    justifyContent: 'center',
    alignItems: 'center',
  },
  joinContainer: {
    width: '90%',
    maxWidth: 400,
  },
  headerContainer: {
    marginBottom: 40,
  },
  headerText: {
    color: 'white',
    fontSize: 32,
    textAlign: 'center',
    marginBottom: 10,
    fontFamily: 'monospace',
  },
  subHeaderText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
  },
  inputContainer: {
    width: '100%',
  },
  input: {
    backgroundColor: 'black',
    color: 'white',
    padding: 15,
    borderRadius: 5,
    marginBottom: 15,
  },
  button: {
    backgroundColor: 'black',
    padding: 15,
    borderRadius: 5,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
  },
});